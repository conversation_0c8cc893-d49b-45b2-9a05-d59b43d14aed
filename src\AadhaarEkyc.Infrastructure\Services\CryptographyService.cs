using AadhaarEkyc.Application.Interfaces;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace AadhaarEkyc.Infrastructure.Services;

public class CryptographyService : ICryptographyService
{
    private readonly ILogger<CryptographyService> _logger;

    public CryptographyService(ILogger<CryptographyService> logger)
    {
        _logger = logger;
    }

    public string EncryptData(string data, string sessionKey)
    {
        try
        {
            _logger.LogDebug("Encrypting data with session key");

            using var aes = Aes.Create();
            aes.Key = Convert.FromBase64String(sessionKey);
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            var dataBytes = Encoding.UTF8.GetBytes(data);
            var encryptedBytes = encryptor.TransformFinalBlock(dataBytes, 0, dataBytes.Length);

            // Combine IV and encrypted data
            var result = new byte[aes.IV.Length + encryptedBytes.Length];
            Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
            Array.Copy(encryptedBytes, 0, result, aes.IV.Length, encryptedBytes.Length);

            return Convert.ToBase64String(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error encrypting data");
            throw;
        }
    }

    public string DecryptData(string encryptedData, string privateKey)
    {
        try
        {
            _logger.LogDebug("Decrypting data with private key");

            var encryptedBytes = Convert.FromBase64String(encryptedData);
            
            using var rsa = RSA.Create();
            rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);

            var decryptedBytes = rsa.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA256);
            return Encoding.UTF8.GetString(decryptedBytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error decrypting data");
            throw;
        }
    }

    public string SignData(string data, string privateKey)
    {
        try
        {
            _logger.LogDebug("Signing data with private key");

            var dataBytes = Encoding.UTF8.GetBytes(data);
            
            using var rsa = RSA.Create();
            rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);

            var signature = rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            return Convert.ToBase64String(signature);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error signing data");
            throw;
        }
    }

    public bool VerifySignature(string data, string signature, string publicKey)
    {
        try
        {
            _logger.LogDebug("Verifying signature with public key");

            var dataBytes = Encoding.UTF8.GetBytes(data);
            var signatureBytes = Convert.FromBase64String(signature);
            
            using var rsa = RSA.Create();
            rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);

            return rsa.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying signature");
            return false;
        }
    }

    public string GenerateSessionKey()
    {
        try
        {
            _logger.LogDebug("Generating new session key");

            using var aes = Aes.Create();
            aes.GenerateKey();
            return Convert.ToBase64String(aes.Key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating session key");
            throw;
        }
    }

    public string EncryptSessionKey(string sessionKey, string publicKey)
    {
        try
        {
            _logger.LogDebug("Encrypting session key with public key");

            var sessionKeyBytes = Convert.FromBase64String(sessionKey);
            
            using var rsa = RSA.Create();
            rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);

            var encryptedSessionKey = rsa.Encrypt(sessionKeyBytes, RSAEncryptionPadding.OaepSHA256);
            return Convert.ToBase64String(encryptedSessionKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error encrypting session key");
            throw;
        }
    }
}
