﻿namespace AadhaarEkyc.Application.Configuration;

public class UidaiOptions
{
    public const string SectionName = "Uidai";

    public string BaseUrl { get; set; } = string.Empty;
    public string OtpEndpoint { get; set; } = "/otp/2.0";
    public string EkycEndpoint { get; set; } = "/ekyc/2.0";
    public string AuaCode { get; set; } = string.Empty;
    public string SubAuaCode { get; set; } = string.Empty;
    public string LicenseKey { get; set; } = string.Empty;
    public string ShareCode { get; set; } = string.Empty;
    public CertificateOptions ClientCert { get; set; } = new();
    public CertificateOptions SignCert { get; set; } = new();
    public string UidaiPublicKeyPath { get; set; } = string.Empty;
    public int TimeoutSeconds { get; set; } = 30;
}

public class CertificateOptions
{
    public string PfxPath { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string? Thumbprint { get; set; }
    public string? StoreName { get; set; }
    public string? StoreLocation { get; set; }
}
