using AadhaarEkyc.Application.Interfaces;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;

namespace AadhaarEkyc.Infrastructure.Services;

public class CertificateService : ICertificateService
{
    private readonly ILogger<CertificateService> _logger;

    public CertificateService(ILogger<CertificateService> logger)
    {
        _logger = logger;
    }

    public X509Certificate2 LoadCertificate(string path, string password)
    {
        try
        {
            _logger.LogInformation("Loading certificate from path: {Path}", path);

            if (!File.Exists(path))
                throw new FileNotFoundException($"Certificate file not found: {path}");

            var certificate = new X509Certificate2(path, password, X509KeyStorageFlags.MachineKeySet | X509KeyStorageFlags.PersistKeySet);
            
            if (!certificate.HasPrivateKey)
                throw new InvalidOperationException("Certificate does not contain a private key");

            _logger.LogInformation("Certificate loaded successfully. Subject: {Subject}", certificate.Subject);
            return certificate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading certificate from path: {Path}", path);
            throw;
        }
    }

    public X509Certificate2 LoadCertificateFromStore(string thumbprint, string storeName, string storeLocation)
    {
        try
        {
            _logger.LogInformation("Loading certificate from store. Thumbprint: {Thumbprint}, Store: {StoreName}, Location: {StoreLocation}", 
                thumbprint, storeName, storeLocation);

            var storeLocationEnum = Enum.Parse<StoreLocation>(storeLocation, true);
            var storeNameEnum = Enum.Parse<StoreName>(storeName, true);

            using var store = new X509Store(storeNameEnum, storeLocationEnum);
            store.Open(OpenFlags.ReadOnly);

            var certificates = store.Certificates.Find(X509FindType.FindByThumbprint, thumbprint, false);
            
            if (certificates.Count == 0)
                throw new InvalidOperationException($"Certificate with thumbprint {thumbprint} not found in store");

            var certificate = certificates[0];
            
            if (!certificate.HasPrivateKey)
                throw new InvalidOperationException("Certificate does not contain a private key");

            _logger.LogInformation("Certificate loaded successfully from store. Subject: {Subject}", certificate.Subject);
            return certificate;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading certificate from store. Thumbprint: {Thumbprint}", thumbprint);
            throw;
        }
    }

    public string GetPublicKey(X509Certificate2 certificate)
    {
        try
        {
            var publicKey = certificate.GetRSAPublicKey();
            if (publicKey == null)
                throw new InvalidOperationException("Certificate does not contain an RSA public key");

            return Convert.ToBase64String(publicKey.ExportRSAPublicKey());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting public key from certificate");
            throw;
        }
    }

    public string GetPrivateKey(X509Certificate2 certificate)
    {
        try
        {
            var privateKey = certificate.GetRSAPrivateKey();
            if (privateKey == null)
                throw new InvalidOperationException("Certificate does not contain an RSA private key");

            return Convert.ToBase64String(privateKey.ExportRSAPrivateKey());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting private key from certificate");
            throw;
        }
    }
}
