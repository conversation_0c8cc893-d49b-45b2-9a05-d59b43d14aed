using AadhaarEkyc.Application.DTOs;
using AadhaarEkyc.Application.Interfaces;
using AadhaarEkyc.Application.Configuration;
using AadhaarEkyc.Domain.ValueObjects;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Xml.Linq;

namespace AadhaarEkyc.Application.Services;

public class EkycService : IEkycService
{
    private readonly IUidaiApiClient _uidaiClient;
    private readonly ICryptographyService _cryptographyService;
    private readonly ICertificateService _certificateService;
    private readonly UidaiOptions _options;
    private readonly ILogger<EkycService> _logger;

    public EkycService(
        IUidaiApiClient uidaiClient,
        ICryptographyService cryptographyService,
        ICertificateService certificateService,
        IOptions<UidaiOptions> options,
        ILogger<EkycService> logger)
    {
        _uidaiClient = uidaiClient;
        _cryptographyService = cryptographyService;
        _certificateService = certificateService;
        _options = options.Value;
        _logger = logger;
    }

    public async Task<OtpResponseDto> InitiateOtpAsync(OtpRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Initiating OTP for Aadhaar: {AadhaarMasked}", MaskAadhaar(request.AadhaarNumber));

            // Validate Aadhaar number
            var aadhaarNumber = new AadhaarNumber(request.AadhaarNumber);

            // Create OTP request XML
            var otpRequestXml = CreateOtpRequestXml(aadhaarNumber.Value, request.ShareCode);

            // Encrypt the request
            var encryptedRequest = await EncryptRequestAsync(otpRequestXml);

            // Send to UIDAI
            var response = await _uidaiClient.SendOtpRequestAsync(encryptedRequest, cancellationToken);

            // Decrypt and parse response
            var decryptedResponse = await DecryptResponseAsync(response);
            return ParseOtpResponse(decryptedResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initiating OTP for Aadhaar: {AadhaarMasked}", MaskAadhaar(request.AadhaarNumber));
            return new OtpResponseDto
            {
                Status = "FAILED",
                Code = "500",
                ErrorMessage = "Internal server error occurred",
                Timestamp = DateTime.UtcNow
            };
        }
    }

    public async Task<EkycResponseDto> PerformEkycAsync(EkycRequestDto request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Performing eKYC for transaction: {TransactionId}", request.TransactionId);

            // Validate Aadhaar number
            var aadhaarNumber = new AadhaarNumber(request.AadhaarNumber);

            // Create eKYC request XML
            var ekycRequestXml = CreateEkycRequestXml(aadhaarNumber.Value, request.Otp, request.TransactionId, request.ShareCode);

            // Encrypt the request
            var encryptedRequest = await EncryptRequestAsync(ekycRequestXml);

            // Send to UIDAI
            var response = await _uidaiClient.SendEkycRequestAsync(encryptedRequest, cancellationToken);

            // Decrypt and parse response
            var decryptedResponse = await DecryptResponseAsync(response);
            return ParseEkycResponse(decryptedResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing eKYC for transaction: {TransactionId}", request.TransactionId);
            return new EkycResponseDto
            {
                Status = "FAILED",
                Code = "500",
                ErrorMessage = "Internal server error occurred",
                Timestamp = DateTime.UtcNow
            };
        }
    }

    private string CreateOtpRequestXml(string aadhaarNumber, string? shareCode)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss");
        var txn = Guid.NewGuid().ToString("N")[..16].ToUpper();

        var xml = new XElement("Otp",
            new XAttribute("uid", aadhaarNumber),
            new XAttribute("tid", "public"),
            new XAttribute("ac", _options.AuaCode),
            new XAttribute("sa", _options.SubAuaCode),
            new XAttribute("lk", _options.LicenseKey),
            new XAttribute("ts", timestamp),
            new XAttribute("txn", txn),
            new XAttribute("ver", "2.0"));

        if (!string.IsNullOrEmpty(shareCode))
        {
            xml.Add(new XAttribute("sc", shareCode));
        }

        return xml.ToString();
    }

    private string CreateEkycRequestXml(string aadhaarNumber, string otp, string transactionId, string? shareCode)
    {
        var timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss");

        var xml = new XElement("Ekyc",
            new XAttribute("uid", aadhaarNumber),
            new XAttribute("tid", "public"),
            new XAttribute("ac", _options.AuaCode),
            new XAttribute("sa", _options.SubAuaCode),
            new XAttribute("lk", _options.LicenseKey),
            new XAttribute("ts", timestamp),
            new XAttribute("txn", transactionId),
            new XAttribute("ver", "2.0"),
            new XElement("Uses",
                new XAttribute("otp", "y"),
                new XAttribute("pin", "n"),
                new XAttribute("bio", "n")),
            new XElement("Tkn",
                new XAttribute("type", "OTP"),
                new XAttribute("value", otp)));

        if (!string.IsNullOrEmpty(shareCode))
        {
            xml.Add(new XAttribute("sc", shareCode));
        }

        return xml.ToString();
    }

    private async Task<string> EncryptRequestAsync(string requestXml)
    {
        // Load UIDAI public key for encryption
        var uidaiPublicKey = await File.ReadAllTextAsync(_options.UidaiPublicKeyPath);
        
        // Generate session key
        var sessionKey = _cryptographyService.GenerateSessionKey();
        
        // Encrypt data with session key
        var encryptedData = _cryptographyService.EncryptData(requestXml, sessionKey);
        
        // Encrypt session key with UIDAI public key
        var encryptedSessionKey = _cryptographyService.EncryptSessionKey(sessionKey, uidaiPublicKey);
        
        // Load signing certificate
        var signingCert = _certificateService.LoadCertificate(_options.SignCert.PfxPath, _options.SignCert.Password);
        var privateKey = _certificateService.GetPrivateKey(signingCert);
        
        // Sign the encrypted data
        var signature = _cryptographyService.SignData(encryptedData, privateKey);
        
        // Create final encrypted request structure
        var encryptedRequest = new
        {
            EncryptedData = encryptedData,
            EncryptedSessionKey = encryptedSessionKey,
            Signature = signature,
            Timestamp = DateTime.UtcNow
        };
        
        return JsonSerializer.Serialize(encryptedRequest);
    }

    private async Task<string> DecryptResponseAsync(string encryptedResponse)
    {
        // Load client certificate for decryption
        var clientCert = _certificateService.LoadCertificate(_options.ClientCert.PfxPath, _options.ClientCert.Password);
        var privateKey = _certificateService.GetPrivateKey(clientCert);
        
        // Decrypt the response
        return _cryptographyService.DecryptData(encryptedResponse, privateKey);
    }

    private OtpResponseDto ParseOtpResponse(string responseXml)
    {
        var xml = XElement.Parse(responseXml);
        
        return new OtpResponseDto
        {
            Status = xml.Attribute("ret")?.Value ?? "FAILED",
            Code = xml.Attribute("code")?.Value ?? "500",
            TransactionId = xml.Attribute("txn")?.Value ?? string.Empty,
            ErrorMessage = xml.Attribute("err")?.Value,
            Timestamp = DateTime.UtcNow
        };
    }

    private EkycResponseDto ParseEkycResponse(string responseXml)
    {
        var xml = XElement.Parse(responseXml);
        
        var response = new EkycResponseDto
        {
            Status = xml.Attribute("ret")?.Value ?? "FAILED",
            Code = xml.Attribute("code")?.Value ?? "500",
            TransactionId = xml.Attribute("txn")?.Value ?? string.Empty,
            ErrorMessage = xml.Attribute("err")?.Value,
            Timestamp = DateTime.UtcNow
        };

        // Parse eKYC data if successful
        if (response.Status == "Y" && xml.Element("UidData")?.Element("Poi") != null)
        {
            response.PersonalInfo = ParsePersonalInfo(xml);
        }

        return response;
    }

    private PersonalInfo ParsePersonalInfo(XElement xml)
    {
        var poi = xml.Element("UidData")?.Element("Poi");
        var poa = xml.Element("UidData")?.Element("Poa");
        var pht = xml.Element("UidData")?.Element("Pht");

        return new PersonalInfo
        {
            Name = poi?.Attribute("name")?.Value ?? string.Empty,
            DateOfBirth = poi?.Attribute("dob")?.Value ?? string.Empty,
            Gender = poi?.Attribute("gender")?.Value ?? string.Empty,
            Phone = poi?.Attribute("phone")?.Value ?? string.Empty,
            Email = poi?.Attribute("email")?.Value ?? string.Empty,
            Photo = pht?.Value,
            Address = new Address
            {
                CareOf = poa?.Attribute("co")?.Value ?? string.Empty,
                Building = poa?.Attribute("house")?.Value ?? string.Empty,
                Street = poa?.Attribute("street")?.Value ?? string.Empty,
                Landmark = poa?.Attribute("lm")?.Value ?? string.Empty,
                Locality = poa?.Attribute("loc")?.Value ?? string.Empty,
                VillageOrCity = poa?.Attribute("vtc")?.Value ?? string.Empty,
                District = poa?.Attribute("dist")?.Value ?? string.Empty,
                State = poa?.Attribute("state")?.Value ?? string.Empty,
                PostOffice = poa?.Attribute("po")?.Value ?? string.Empty,
                Country = poa?.Attribute("country")?.Value ?? string.Empty,
                Pincode = poa?.Attribute("pc")?.Value ?? string.Empty
            }
        };
    }

    private static string MaskAadhaar(string aadhaar)
    {
        if (string.IsNullOrEmpty(aadhaar) || aadhaar.Length < 8)
            return "****";
        
        return $"****{aadhaar[^4..]}";
    }
}
