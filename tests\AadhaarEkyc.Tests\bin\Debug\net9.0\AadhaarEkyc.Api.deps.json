{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"AadhaarEkyc.Api/1.0.0": {"dependencies": {"AadhaarEkyc.Application": "1.0.0", "AadhaarEkyc.Infrastructure": "1.0.0", "Microsoft.AspNetCore.OpenApi": "9.0.0", "Swashbuckle.AspNetCore": "9.0.3"}, "runtime": {"AadhaarEkyc.Api.dll": {}}}, "Microsoft.AspNetCore.OpenApi/9.0.0": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {}, "Microsoft.Extensions.Configuration/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Diagnostics/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.8", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Http/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Diagnostics": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Options/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/9.0.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "9.0.0", "Swashbuckle.AspNetCore.Swagger": "9.0.3", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.3", "Swashbuckle.AspNetCore.SwaggerUI": "9.0.3"}}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.3"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.3.1613"}}}, "AadhaarEkyc.Application/1.0.0": {"dependencies": {"AadhaarEkyc.Domain": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"AadhaarEkyc.Application.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "AadhaarEkyc.Domain/1.0.0": {"runtime": {"AadhaarEkyc.Domain.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "AadhaarEkyc.Infrastructure/1.0.0": {"dependencies": {"AadhaarEkyc.Application": "1.0.0", "Microsoft.Extensions.Http": "9.0.8", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8"}, "runtime": {"AadhaarEkyc.Infrastructure.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"AadhaarEkyc.Api/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OpenApi/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FqUK5j1EOPNuFT7IafltZQ3cakqhSwVzH5ZW1MhZDe4pPXs9sJ2M5jom1Omsu+mwF2tNKKlRAzLRHQTZzbd+6Q==", "path": "microsoft.aspnetcore.openapi/9.0.0", "hashPath": "microsoft.aspnetcore.openapi.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Kzzf7pRey40VaUkHN9/uWxrKVkLu2AQjt+GVeeKLLpiEHAJ1xZRsLSh4ZZYEnyS7Kt2OBOPmsXNdU+wbcOl5w==", "path": "microsoft.extensions.apidescription.server/9.0.0", "hashPath": "microsoft.extensions.apidescription.server.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6m+8Xgmf8UWL0p/oGqBM+0KbHE5/ePXbV1hKXgC59zEv0aa0DW5oiiyxDbK5kH5j4gIvyD5uWL0+HadKBJngvQ==", "path": "microsoft.extensions.configuration/9.0.8", "hashPath": "microsoft.extensions.configuration.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-yNou2KM35RvzOh4vUFtl2l33rWPvOCoba+nzEDJ+BgD8aOL/jew4WPCibQvntRfOJ2pJU8ARygSMD+pdjvDHuA==", "path": "microsoft.extensions.configuration.abstractions/9.0.8", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-0vK9DnYrYChdiH3yRZWkkp4x4LbrfkWEdBc5HOsQ8t/0CLOWKXKkkhOE8A1shlex0hGydbGrhObeypxz/QTm+w==", "path": "microsoft.extensions.configuration.binder/9.0.8", "hashPath": "microsoft.extensions.configuration.binder.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-JJjI2Fa+QtZcUyuNjbKn04OjIUX5IgFGFu/Xc+qvzh1rXdZHLcnqqVXhR4093bGirTwacRlHiVg1XYI9xum6QQ==", "path": "microsoft.extensions.dependencyinjection/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-BKkLCFXzJvNmdngeYBf72VXoZqTJSb1orvjdzDLaGobicoGFBPW8ug2ru1nnEewMEwJzMgnsjHQY8EaKWmVhKg==", "path": "microsoft.extensions.diagnostics/9.0.8", "hashPath": "microsoft.extensions.diagnostics.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-UDY7blv4DCyIJ/8CkNrQKLaAZFypXQavRZ2DWf/2zi1mxYYKKw2t8AOCBWxNntyPZHPGhtEmL3snFM98ADZqTw==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.8", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-jDj+4aDByk47oESlDDTtk6LWzlXlmoCsjCn6ihd+i9OntN885aPLszUII5+w0B/7wYSZcS3KdjqLAIhKLSiBXQ==", "path": "microsoft.extensions.http/9.0.8", "hashPath": "microsoft.extensions.http.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Z/7ze+0iheT7FJeZPqJKARYvyC2bmwu3whbm/48BJjdlGVvgDguoCqJIkI/67NkroTYobd5geai1WheNQvWrgA==", "path": "microsoft.extensions.logging/9.0.8", "hashPath": "microsoft.extensions.logging.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-pYnAffJL7ARD/HCnnPvnFKSIHnTSmWz84WIlT9tPeQ4lHNiu0Az7N/8itihWvcF8sT+VVD5lq8V+ckMzu4SbOw==", "path": "microsoft.extensions.logging.abstractions/9.0.8", "hashPath": "microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OmTaQ0v4gxGQkehpwWIqPoEiwsPuG/u4HUsbOFoWGx4DKET2AXzopnFe/fE608FIhzc/kcg2p8JdyMRCCUzitQ==", "path": "microsoft.extensions.options/9.0.8", "hashPath": "microsoft.extensions.options.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-eW2s6n06x0w6w4nsX+SvpgsFYkl+Y0CttYAt6DKUXeqprX+hzNqjSfOh637fwNJBg7wRBrOIRHe49gKiTgJxzQ==", "path": "microsoft.extensions.options.configurationextensions/9.0.8", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Swashbuckle.AspNetCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Akk4oFgy0ST8Q8pZTfPbrt045tWNyMMiKhlbYjG3qnjQZLz645IL5vhQm7NLicc2sAAQ+vftArIlsYWFevmb2g==", "path": "swashbuckle.aspnetcore/9.0.3", "hashPath": "swashbuckle.aspnetcore.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-CGpkZDWj1g/yH/0wYkxUtBhiFo5TY/Esq2fS0vlBvLOs1UL2Jzef9tdtYmTdd3zBPtnMyXQcsXjMt9yCxz4VaA==", "path": "swashbuckle.aspnetcore.swagger/9.0.3", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-STqjhw1TZiEGmIRgE6jcJUOcgU/Fjquc6dP4GqbuwBzqWZAWr/9T7FZOGWYEwKnmkMplzlUNepGHwnUrfTP0fw==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-DgJKJASz5OAygeKv2+N0FCZVhQylESqLXrtrRAqIT0vKpX7t5ImJ1FL6+6OqxKiamGkL0jchRXR8OgpMSsMh8w==", "path": "swashbuckle.aspnetcore.swaggerui/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.9.0.3.nupkg.sha512"}, "AadhaarEkyc.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AadhaarEkyc.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AadhaarEkyc.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}