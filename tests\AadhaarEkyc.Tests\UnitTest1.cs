﻿using AadhaarEkyc.Domain.ValueObjects;

namespace AadhaarEkyc.Tests;

public class AadhaarNumberTests
{
    [Fact]
    public void AadhaarNumber_ValidNumber_ShouldCreateSuccessfully()
    {
        // Arrange
        var validAadhaar = "************"; // This is a test number with valid checksum

        // Act & Assert
        var aadhaarNumber = new AadhaarNumber(validAadhaar);
        Assert.Equal(validAadhaar, aadhaarNumber.Value);
    }

    [Fact]
    public void AadhaarNumber_InvalidFormat_ShouldThrowException()
    {
        // Arrange
        var invalidAadhaar = "12345";

        // Act & Assert
        Assert.Throws<ArgumentException>(() => new AadhaarNumber(invalidAadhaar));
    }

    [Fact]
    public void AadhaarNumber_EmptyString_ShouldThrowException()
    {
        // Arrange
        var emptyAadhaar = "";

        // Act & Assert
        Assert.Throws<ArgumentException>(() => new AadhaarNumber(emptyAadhaar));
    }

    [Fact]
    public void AadhaarNumber_WithSpaces_ShouldRemoveSpaces()
    {
        // Arrange
        var aadhaarWithSpaces = "**************";

        // Act
        var aadhaarNumber = new AadhaarNumber(aadhaarWithSpaces);

        // Assert
        Assert.Equal("************", aadhaarNumber.Value);
    }
}
