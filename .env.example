# A<PERSON>haar eKYC API Configuration
# Copy this file to .env and update with your actual values

# UIDAI Configuration
UIDAI__BASEURL=https://preprod.uidai.gov.in
UIDAI__AUACODE=YOUR_AUA_CODE
UIDAI__SUBAUACODE=YOUR_SUB_AUA_CODE
UIDAI__LICENSEKEY=YOUR_LICENSE_KEY
UIDAI__SHARECODE=YOUR_SHARE_CODE

# Certificate Configuration
UIDAI__CLIENTCERT__PFXPATH=certs/client.pfx
UIDAI__CLIENTCERT__PASSWORD=your_client_cert_password
UIDAI__SIGNCERT__PFXPATH=certs/signing.pfx
UIDAI__SIGNCERT__PASSWORD=your_signing_cert_password
UIDAI__UIDAIPUBLICKEYPATH=certs/uidai_public.cer

# Optional: Use certificate store instead of PFX files
# UIDAI__CLIENTCERT__THUMBPRINT=your_client_cert_thumbprint
# UIDAI__CLIENTCERT__STORENAME=My
# UIDAI__CLIENTCERT__STORELOCATION=CurrentUser

# API Configuration
UIDAI__TIMEOUTSECONDS=30

# Logging
LOGGING__LOGLEVEL__DEFAULT=Information
LOGGING__LOGLEVEL__AADHAREKYC=Debug
