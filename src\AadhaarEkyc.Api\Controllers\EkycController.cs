using AadhaarEkyc.Application.DTOs;
using AadhaarEkyc.Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace AadhaarEkyc.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class EkycController : ControllerBase
{
    private readonly IEkycService _ekycService;
    private readonly ILogger<EkycController> _logger;

    public EkycController(IEkycService ekycService, ILogger<EkycController> logger)
    {
        _ekycService = ekycService;
        _logger = logger;
    }

    /// <summary>
    /// Initiates OTP for Aadhaar eKYC
    /// </summary>
    /// <param name="request">OTP request containing Aadhaar number</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>OTP response with transaction ID</returns>
    [HttpPost("otp/initiate")]
    [ProducesResponseType(typeof(OtpResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<OtpResponseDto>> InitiateOtp(
        [FromBody] OtpRequestDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("OTP initiation request received for Aadhaar: {AadhaarMasked}", 
                MaskAadhaar(request.AadhaarNumber));

            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiErrorResponse
                {
                    Error = "ValidationError",
                    Message = "Invalid request data",
                    Details = string.Join("; ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage))
                });
            }

            var response = await _ekycService.InitiateOtpAsync(request, cancellationToken);
            
            _logger.LogInformation("OTP initiation completed for Aadhaar: {AadhaarMasked}, Status: {Status}", 
                MaskAadhaar(request.AadhaarNumber), response.Status);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid Aadhaar number provided: {AadhaarMasked}", 
                MaskAadhaar(request.AadhaarNumber));

            return BadRequest(new ApiErrorResponse
            {
                Error = "InvalidAadhaar",
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initiating OTP for Aadhaar: {AadhaarMasked}", 
                MaskAadhaar(request.AadhaarNumber));

            return StatusCode(StatusCodes.Status500InternalServerError, new ApiErrorResponse
            {
                Error = "InternalError",
                Message = "An internal error occurred while processing the request"
            });
        }
    }

    /// <summary>
    /// Performs eKYC using OTP
    /// </summary>
    /// <param name="request">eKYC request containing Aadhaar number, OTP, and transaction ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>eKYC response with personal information</returns>
    [HttpPost("verify")]
    [ProducesResponseType(typeof(EkycResponseDto), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiErrorResponse), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<EkycResponseDto>> PerformEkyc(
        [FromBody] EkycRequestDto request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("eKYC request received for transaction: {TransactionId}", 
                request.TransactionId);

            if (!ModelState.IsValid)
            {
                return BadRequest(new ApiErrorResponse
                {
                    Error = "ValidationError",
                    Message = "Invalid request data",
                    Details = string.Join("; ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage))
                });
            }

            var response = await _ekycService.PerformEkycAsync(request, cancellationToken);
            
            _logger.LogInformation("eKYC completed for transaction: {TransactionId}, Status: {Status}", 
                request.TransactionId, response.Status);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid request data for transaction: {TransactionId}", 
                request.TransactionId);

            return BadRequest(new ApiErrorResponse
            {
                Error = "InvalidRequest",
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing eKYC for transaction: {TransactionId}", 
                request.TransactionId);

            return StatusCode(StatusCodes.Status500InternalServerError, new ApiErrorResponse
            {
                Error = "InternalError",
                Message = "An internal error occurred while processing the request"
            });
        }
    }

    /// <summary>
    /// Health check endpoint
    /// </summary>
    /// <returns>API health status</returns>
    [HttpGet("health")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public ActionResult GetHealth()
    {
        return Ok(new
        {
            Status = "Healthy",
            Timestamp = DateTime.UtcNow,
            Version = "1.0.0"
        });
    }

    private static string MaskAadhaar(string aadhaar)
    {
        if (string.IsNullOrEmpty(aadhaar) || aadhaar.Length < 8)
            return "****";
        
        return $"****{aadhaar[^4..]}";
    }
}
