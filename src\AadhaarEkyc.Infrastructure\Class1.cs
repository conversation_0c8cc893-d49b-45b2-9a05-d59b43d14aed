﻿using AadhaarEkyc.Application.Interfaces;
using AadhaarEkyc.Application.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Text;

namespace AadhaarEkyc.Infrastructure.Services;

public class UidaiApiClient : IUidaiApiClient
{
    private readonly HttpClient _httpClient;
    private readonly UidaiOptions _options;
    private readonly ILogger<UidaiApiClient> _logger;

    public UidaiApiClient(HttpClient httpClient, IOptions<UidaiOptions> options, ILogger<UidaiApiClient> logger)
    {
        _httpClient = httpClient;
        _options = options.Value;
        _logger = logger;

        _httpClient.BaseAddress = new Uri(_options.BaseUrl);
        _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);
    }

    public async Task<string> SendOtpRequestAsync(string encryptedRequest, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending OTP request to UIDAI");

            var content = new StringContent(encryptedRequest, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(_options.OtpEndpoint, content, cancellationToken);

            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            _logger.LogInformation("Received OTP response from UIDAI");
            return responseContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending OTP request to UIDAI");
            throw;
        }
    }

    public async Task<string> SendEkycRequestAsync(string encryptedRequest, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Sending eKYC request to UIDAI");

            var content = new StringContent(encryptedRequest, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(_options.EkycEndpoint, content, cancellationToken);

            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            _logger.LogInformation("Received eKYC response from UIDAI");
            return responseContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending eKYC request to UIDAI");
            throw;
        }
    }
}
