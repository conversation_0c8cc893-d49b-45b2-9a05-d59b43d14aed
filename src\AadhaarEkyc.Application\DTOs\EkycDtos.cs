using System.ComponentModel.DataAnnotations;

namespace AadhaarEkyc.Application.DTOs;

public class OtpRequestDto
{
    [Required]
    [StringLength(12, MinimumLength = 12)]
    [RegularExpression(@"^\d{12}$", ErrorMessage = "Aadhaar number must be 12 digits")]
    public string A<PERSON>haarNumber { get; set; } = string.Empty;

    [StringLength(4, MinimumLength = 4)]
    public string? ShareCode { get; set; }
}

public class OtpResponseDto
{
    public string Status { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; }
}

public class EkycRequestDto
{
    [Required]
    [StringLength(12, MinimumLength = 12)]
    [RegularExpression(@"^\d{12}$", ErrorMessage = "Aadhaar number must be 12 digits")]
    public string AadhaarNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(6, MinimumLength = 6)]
    [RegularExpression(@"^\d{6}$", ErrorMessage = "OTP must be 6 digits")]
    public string Otp { get; set; } = string.Empty;

    [Required]
    public string TransactionId { get; set; } = string.Empty;

    [StringLength(4, MinimumLength = 4)]
    public string? ShareCode { get; set; }
}

public class EkycResponseDto
{
    public string Status { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string TransactionId { get; set; } = string.Empty;
    public PersonalInfo? PersonalInfo { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime Timestamp { get; set; }
}

public class PersonalInfo
{
    public string Name { get; set; } = string.Empty;
    public string DateOfBirth { get; set; } = string.Empty;
    public string Gender { get; set; } = string.Empty;
    public string Phone { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public Address Address { get; set; } = new();
    public string? Photo { get; set; } // Base64 encoded
}

public class Address
{
    public string CareOf { get; set; } = string.Empty;
    public string Building { get; set; } = string.Empty;
    public string Street { get; set; } = string.Empty;
    public string Landmark { get; set; } = string.Empty;
    public string Locality { get; set; } = string.Empty;
    public string VillageOrCity { get; set; } = string.Empty;
    public string District { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string PostOffice { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string Pincode { get; set; } = string.Empty;
}

public class ApiErrorResponse
{
    public string Error { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? Details { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
