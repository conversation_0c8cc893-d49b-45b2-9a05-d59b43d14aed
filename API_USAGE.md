# Aadhaar eKYC API Usage Guide

## Quick Start

1. **Configure the application**:
   - Update `appsettings.json` with your UIDAI credentials
   - Place your certificates in the `certs` folder
   - Set certificate passwords via environment variables

2. **Run the application**:
   ```bash
   cd src/AadhaarEkyc.Api
   dotnet run
   ```

3. **Access Swagger UI**: Navigate to `https://localhost:5001/swagger`

## API Endpoints

### 1. Initiate OTP

**Endpoint**: `POST /api/ekyc/otp/initiate`

**Request**:
```json
{
  "aadhaarNumber": "************",
  "shareCode": "1234"
}
```

**Response**:
```json
{
  "status": "Y",
  "code": "200",
  "transactionId": "TXN1234567890",
  "errorMessage": null,
  "timestamp": "2025-08-11T10:30:00Z"
}
```

**cURL Example**:
```bash
curl -X POST "https://localhost:5001/api/ekyc/otp/initiate" \
  -H "Content-Type: application/json" \
  -d '{
    "aadhaarNumber": "************",
    "shareCode": "1234"
  }'
```

### 2. Perform eKYC

**Endpoint**: `POST /api/ekyc/verify`

**Request**:
```json
{
  "aadhaarNumber": "************",
  "otp": "123456",
  "transactionId": "TXN1234567890",
  "shareCode": "1234"
}
```

**Response**:
```json
{
  "status": "Y",
  "code": "200",
  "transactionId": "TXN1234567890",
  "personalInfo": {
    "name": "John Doe",
    "dateOfBirth": "01-01-1990",
    "gender": "M",
    "phone": "9876543210",
    "email": "<EMAIL>",
    "address": {
      "careOf": "S/O Father Name",
      "building": "House No 123",
      "street": "Main Street",
      "locality": "Sector 1",
      "villageOrCity": "City Name",
      "district": "District Name",
      "state": "State Name",
      "pincode": "123456",
      "country": "India"
    },
    "photo": "base64_encoded_photo_data"
  },
  "errorMessage": null,
  "timestamp": "2025-08-11T10:35:00Z"
}
```

**cURL Example**:
```bash
curl -X POST "https://localhost:5001/api/ekyc/verify" \
  -H "Content-Type: application/json" \
  -d '{
    "aadhaarNumber": "************",
    "otp": "123456",
    "transactionId": "TXN1234567890",
    "shareCode": "1234"
  }'
```

### 3. Health Check

**Endpoint**: `GET /api/ekyc/health`

**Response**:
```json
{
  "status": "Healthy",
  "timestamp": "2025-08-11T10:30:00Z",
  "version": "1.0.0"
}
```

## Error Responses

### Validation Error
```json
{
  "error": "ValidationError",
  "message": "Invalid request data",
  "details": "Aadhaar number must be 12 digits",
  "timestamp": "2025-08-11T10:30:00Z"
}
```

### Invalid Aadhaar
```json
{
  "error": "InvalidAadhaar",
  "message": "Invalid Aadhaar number format. Must be 12 digits.",
  "timestamp": "2025-08-11T10:30:00Z"
}
```

### Internal Error
```json
{
  "error": "InternalError",
  "message": "An internal error occurred while processing the request",
  "timestamp": "2025-08-11T10:30:00Z"
}
```

## Configuration Examples

### Environment Variables
```bash
# UIDAI Configuration
export UIDAI__BASEURL="https://prod.uidai.gov.in"
export UIDAI__AUACODE="YOUR_PRODUCTION_AUA_CODE"
export UIDAI__SUBAUACODE="YOUR_PRODUCTION_SUB_AUA_CODE"
export UIDAI__LICENSEKEY="YOUR_PRODUCTION_LICENSE_KEY"

# Certificate Passwords
export UIDAI__CLIENTCERT__PASSWORD="your_client_cert_password"
export UIDAI__SIGNCERT__PASSWORD="your_signing_cert_password"
```

### Docker Compose
```yaml
version: '3.8'
services:
  aadhaar-ekyc-api:
    build: .
    ports:
      - "5000:8080"
    environment:
      - UIDAI__BASEURL=https://preprod.uidai.gov.in
      - UIDAI__AUACODE=YOUR_AUA_CODE
      - UIDAI__CLIENTCERT__PASSWORD=your_cert_password
    volumes:
      - ./certs:/app/certs:ro
```

## Integration Examples

### C# Client
```csharp
using System.Text.Json;

var client = new HttpClient();
client.BaseAddress = new Uri("https://localhost:5001");

// Initiate OTP
var otpRequest = new { aadhaarNumber = "************", shareCode = "1234" };
var otpResponse = await client.PostAsJsonAsync("/api/ekyc/otp/initiate", otpRequest);
var otpResult = await otpResponse.Content.ReadFromJsonAsync<OtpResponseDto>();

// Perform eKYC
var ekycRequest = new { 
    aadhaarNumber = "************", 
    otp = "123456", 
    transactionId = otpResult.TransactionId,
    shareCode = "1234"
};
var ekycResponse = await client.PostAsJsonAsync("/api/ekyc/verify", ekycRequest);
var ekycResult = await ekycResponse.Content.ReadFromJsonAsync<EkycResponseDto>();
```

### JavaScript/Node.js
```javascript
const axios = require('axios');

const baseURL = 'https://localhost:5001';

// Initiate OTP
const otpResponse = await axios.post(`${baseURL}/api/ekyc/otp/initiate`, {
  aadhaarNumber: '************',
  shareCode: '1234'
});

// Perform eKYC
const ekycResponse = await axios.post(`${baseURL}/api/ekyc/verify`, {
  aadhaarNumber: '************',
  otp: '123456',
  transactionId: otpResponse.data.transactionId,
  shareCode: '1234'
});

console.log('eKYC Result:', ekycResponse.data);
```

### Python
```python
import requests

base_url = 'https://localhost:5001'

# Initiate OTP
otp_response = requests.post(f'{base_url}/api/ekyc/otp/initiate', json={
    'aadhaarNumber': '************',
    'shareCode': '1234'
})

# Perform eKYC
ekyc_response = requests.post(f'{base_url}/api/ekyc/verify', json={
    'aadhaarNumber': '************',
    'otp': '123456',
    'transactionId': otp_response.json()['transactionId'],
    'shareCode': '1234'
})

print('eKYC Result:', ekyc_response.json())
```

## Security Best Practices

1. **Always use HTTPS** in production
2. **Store certificates securely** and use strong passwords
3. **Use environment variables** for sensitive configuration
4. **Implement rate limiting** to prevent abuse
5. **Log security events** but mask sensitive data
6. **Validate all inputs** on both client and server side
7. **Use proper error handling** without exposing internal details

## Monitoring and Logging

The API includes comprehensive logging. Key log events:
- OTP initiation requests (with masked Aadhaar numbers)
- eKYC verification attempts
- Certificate loading events
- Cryptographic operations
- UIDAI API communication
- Error conditions and exceptions

Configure log levels in `appsettings.json`:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "AadhaarEkyc": "Debug"
    }
  }
}
```
