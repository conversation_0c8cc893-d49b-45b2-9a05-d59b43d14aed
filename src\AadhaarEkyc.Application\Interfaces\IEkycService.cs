using AadhaarEkyc.Application.DTOs;

namespace AadhaarEkyc.Application.Interfaces;

public interface IEkycService
{
    Task<OtpResponseDto> InitiateOtpAsync(OtpRequestDto request, CancellationToken cancellationToken = default);
    Task<EkycResponseDto> PerformEkycAsync(EkycRequestDto request, CancellationToken cancellationToken = default);
}

public interface ICryptographyService
{
    string EncryptData(string data, string publicKey);
    string DecryptData(string encryptedData, string privateKey);
    string SignData(string data, string privateKey);
    bool VerifySignature(string data, string signature, string publicKey);
    string GenerateSessionKey();
    string EncryptSessionKey(string sessionKey, string publicKey);
}

public interface ICertificateService
{
    System.Security.Cryptography.X509Certificates.X509Certificate2 LoadCertificate(string path, string password);
    System.Security.Cryptography.X509Certificates.X509Certificate2 LoadCertificateFromStore(string thumbprint, string storeName, string storeLocation);
    string GetPublicKey(System.Security.Cryptography.X509Certificates.X509Certificate2 certificate);
    string GetPrivateKey(System.Security.Cryptography.X509Certificates.X509Certificate2 certificate);
}

public interface IUidaiApiClient
{
    Task<string> SendOtpRequestAsync(string encryptedRequest, CancellationToken cancellationToken = default);
    Task<string> SendEkycRequestAsync(string encryptedRequest, CancellationToken cancellationToken = default);
}
