using AadhaarEkyc.Application.Interfaces;
using AadhaarEkyc.Application.Configuration;
using AadhaarEkyc.Infrastructure.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System.Security.Cryptography.X509Certificates;

namespace AadhaarEkyc.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure options
        services.Configure<UidaiOptions>(configuration.GetSection(UidaiOptions.SectionName));

        // Register services
        services.AddScoped<ICertificateService, CertificateService>();
        services.AddScoped<ICryptographyService, CryptographyService>();

        // Configure HttpClient for UIDAI API
        services.AddHttpClient<IUidaiApiClient, UidaiApiClient>((serviceProvider, client) =>
        {
            var options = serviceProvider.GetRequiredService<IOptions<UidaiOptions>>().Value;
            var certificateService = serviceProvider.GetRequiredService<ICertificateService>();

            client.BaseAddress = new Uri(options.BaseUrl);
            client.Timeout = TimeSpan.FromSeconds(options.TimeoutSeconds);

            // Add default headers
            client.DefaultRequestHeaders.Add("User-Agent", "AadhaarEkycApi/1.0");
            client.DefaultRequestHeaders.Add("Accept", "application/json");
        })
        .ConfigurePrimaryHttpMessageHandler((serviceProvider) =>
        {
            var options = serviceProvider.GetRequiredService<IOptions<UidaiOptions>>().Value;
            var certificateService = serviceProvider.GetRequiredService<ICertificateService>();

            var handler = new HttpClientHandler();

            // Configure client certificate if provided
            if (!string.IsNullOrEmpty(options.ClientCert.PfxPath))
            {
                try
                {
                    X509Certificate2 clientCert;
                    
                    if (!string.IsNullOrEmpty(options.ClientCert.Thumbprint))
                    {
                        // Load from certificate store
                        clientCert = certificateService.LoadCertificateFromStore(
                            options.ClientCert.Thumbprint,
                            options.ClientCert.StoreName ?? "My",
                            options.ClientCert.StoreLocation ?? "CurrentUser");
                    }
                    else
                    {
                        // Load from PFX file
                        clientCert = certificateService.LoadCertificate(
                            options.ClientCert.PfxPath,
                            options.ClientCert.Password);
                    }

                    handler.ClientCertificates.Add(clientCert);
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException("Failed to load client certificate", ex);
                }
            }

            // Configure SSL/TLS settings
            handler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) =>
            {
                // In production, implement proper certificate validation
                // For now, accept all certificates (NOT recommended for production)
                return true;
            };

            return handler;
        });

        return services;
    }
}
