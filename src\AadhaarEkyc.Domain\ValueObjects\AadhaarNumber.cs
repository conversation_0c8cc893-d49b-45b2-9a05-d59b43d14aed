using System.Text.RegularExpressions;

namespace AadhaarEkyc.Domain.ValueObjects;

public class AadhaarNumber
{
    private static readonly Regex AadhaarPattern = new(@"^\d{12}$", RegexOptions.Compiled);
    
    public string Value { get; }

    public AadhaarNumber(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Aadhaar number cannot be null or empty", nameof(value));

        var cleanValue = value.Replace(" ", "").Replace("-", "");
        
        if (!AadhaarPattern.IsMatch(cleanValue))
            throw new ArgumentException("Invalid Aadhaar number format. Must be 12 digits.", nameof(value));

        if (!Is<PERSON>alid<PERSON>hecksum(cleanValue))
            throw new ArgumentException("Invalid Aadhaar number checksum", nameof(value));

        Value = cleanValue;
    }

    private static bool IsValidChecksum(string aadhaar)
    {
        // <PERSON><PERSON><PERSON><PERSON> algorithm for Aadhaar validation
        var d = new int[,] {
            {0, 1, 2, 3, 4, 5, 6, 7, 8, 9},
            {1, 2, 3, 4, 0, 6, 7, 8, 9, 5},
            {2, 3, 4, 0, 1, 7, 8, 9, 5, 6},
            {3, 4, 0, 1, 2, 8, 9, 5, 6, 7},
            {4, 0, 1, 2, 3, 9, 5, 6, 7, 8},
            {5, 9, 8, 7, 6, 0, 4, 3, 2, 1},
            {6, 5, 9, 8, 7, 1, 0, 4, 3, 2},
            {7, 6, 5, 9, 8, 2, 1, 0, 4, 3},
            {8, 7, 6, 5, 9, 3, 2, 1, 0, 4},
            {9, 8, 7, 6, 5, 4, 3, 2, 1, 0}
        };

        var p = new int[,] {
            {0, 1, 2, 3, 4, 5, 6, 7, 8, 9},
            {1, 5, 7, 6, 2, 8, 3, 0, 9, 4},
            {5, 8, 0, 3, 7, 9, 6, 1, 4, 2},
            {8, 9, 1, 6, 0, 4, 3, 5, 2, 7},
            {9, 4, 5, 3, 1, 2, 6, 8, 7, 0},
            {4, 2, 8, 6, 5, 7, 3, 9, 0, 1},
            {2, 7, 9, 3, 8, 0, 6, 4, 1, 5},
            {7, 0, 4, 6, 9, 1, 3, 2, 5, 8}
        };

        var inv = new[] { 0, 4, 3, 2, 1, 5, 6, 7, 8, 9 };

        var c = 0;
        var myArray = aadhaar.ToCharArray().Reverse().ToArray();

        for (var i = 0; i < myArray.Length; i++)
        {
            c = d[c, p[i % 8, int.Parse(myArray[i].ToString())]];
        }

        return c == 0;
    }

    public override string ToString() => Value;

    public override bool Equals(object? obj)
    {
        return obj is AadhaarNumber other && Value == other.Value;
    }

    public override int GetHashCode() => Value.GetHashCode();

    public static implicit operator string(AadhaarNumber aadhaar) => aadhaar.Value;
    public static explicit operator AadhaarNumber(string value) => new(value);
}
