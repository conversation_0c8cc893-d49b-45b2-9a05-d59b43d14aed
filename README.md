# Aadhaar eKYC API

A .NET 8 Web API implementation for Aadhaar eKYC 2.0 integration with clean architecture principles.

## Architecture

This solution follows Clean Architecture principles with the following layers:

- **AadhaarEkyc.Api**: Web API layer with controllers and configuration
- **AadhaarEkyc.Application**: Business logic, DTOs, and service interfaces
- **AadhaarEkyc.Domain**: Domain entities and value objects
- **AadhaarEkyc.Infrastructure**: External service implementations (HTTP clients, cryptography, certificates)

## Features

- ✅ Clean Architecture implementation
- ✅ Configurable URLs, keys, and certificates
- ✅ OTP initiation for Aadhaar eKYC
- ✅ eKYC verification with OTP
- ✅ RSA encryption/decryption and digital signatures
- ✅ X.509 certificate management (PFX files and certificate store)
- ✅ Comprehensive logging and error handling
- ✅ Input validation and Aadhaar number verification
- ✅ Health checks
- ✅ Swagger/OpenAPI documentation
- ✅ CORS support

## Configuration

### Environment Variables

You can override any configuration using environment variables with the pattern `UIDAI__<PropertyName>`:

```bash
UIDAI__BASEURL=https://prod.uidai.gov.in
UIDAI__AUACODE=YOUR_PRODUCTION_AUA_CODE
UIDAI__CLIENTCERT__PASSWORD=your_cert_password
```

### appsettings.json

```json
{
  "Uidai": {
    "BaseUrl": "https://preprod.uidai.gov.in",
    "OtpEndpoint": "/otp/2.0",
    "EkycEndpoint": "/ekyc/2.0",
    "AuaCode": "YOUR_AUA_CODE",
    "SubAuaCode": "YOUR_SUB_AUA_CODE",
    "LicenseKey": "YOUR_LICENSE_KEY",
    "ShareCode": "YOUR_SHARE_CODE",
    "TimeoutSeconds": 30,
    "ClientCert": {
      "PfxPath": "certs/client.pfx",
      "Password": "cert_password",
      "Thumbprint": "",
      "StoreName": "My",
      "StoreLocation": "CurrentUser"
    },
    "SignCert": {
      "PfxPath": "certs/signing.pfx",
      "Password": "cert_password"
    },
    "UidaiPublicKeyPath": "certs/uidai_public.cer"
  }
}
```

### Certificate Configuration

You can configure certificates in two ways:

1. **PFX Files**: Provide `PfxPath` and `Password`
2. **Certificate Store**: Provide `Thumbprint`, `StoreName`, and `StoreLocation`

## API Endpoints

### 1. Initiate OTP

**POST** `/api/ekyc/otp/initiate`

```json
{
  "aadhaarNumber": "123456789012",
  "shareCode": "1234"
}
```

**Response:**
```json
{
  "status": "Y",
  "code": "200",
  "transactionId": "TXN123456789",
  "errorMessage": null,
  "timestamp": "2025-08-11T10:30:00Z"
}
```

### 2. Perform eKYC

**POST** `/api/ekyc/verify`

```json
{
  "aadhaarNumber": "123456789012",
  "otp": "123456",
  "transactionId": "TXN123456789",
  "shareCode": "1234"
}
```

**Response:**
```json
{
  "status": "Y",
  "code": "200",
  "transactionId": "TXN123456789",
  "personalInfo": {
    "name": "John Doe",
    "dateOfBirth": "01-01-1990",
    "gender": "M",
    "phone": "**********",
    "email": "<EMAIL>",
    "address": {
      "careOf": "S/O Father Name",
      "building": "House No 123",
      "street": "Main Street",
      "locality": "Sector 1",
      "villageOrCity": "City Name",
      "district": "District Name",
      "state": "State Name",
      "pincode": "123456",
      "country": "India"
    },
    "photo": "base64_encoded_photo"
  },
  "errorMessage": null,
  "timestamp": "2025-08-11T10:35:00Z"
}
```

### 3. Health Check

**GET** `/api/ekyc/health`

**Response:**
```json
{
  "status": "Healthy",
  "timestamp": "2025-08-11T10:30:00Z",
  "version": "1.0.0"
}
```

## Setup Instructions

### Prerequisites

- .NET 8 SDK
- Valid UIDAI AUA/Sub-AUA credentials
- Client and signing certificates in PFX format
- UIDAI public key certificate

### 1. Clone and Build

```bash
git clone <repository-url>
cd Aadhaar_ekyc_api
dotnet restore
dotnet build
```

### 2. Configure Certificates

Create a `certs` folder in the API project and place your certificates:

```
src/AadhaarEkyc.Api/certs/
├── client.pfx          # Client certificate for mTLS
├── signing.pfx         # Signing certificate for request signing
└── uidai_public.cer    # UIDAI public key for encryption
```

### 3. Update Configuration

Update `appsettings.json` or use environment variables to configure:

- UIDAI endpoints and credentials
- Certificate paths and passwords
- Timeout settings

### 4. Run the Application

```bash
cd src/AadhaarEkyc.Api
dotnet run
```

The API will be available at:
- HTTP: `http://localhost:5000`
- HTTPS: `https://localhost:5001`
- Swagger UI: `https://localhost:5001/swagger`

## Security Considerations

1. **Certificate Security**: Store certificates securely and use strong passwords
2. **Environment Variables**: Use environment variables or Azure Key Vault for sensitive configuration
3. **HTTPS**: Always use HTTPS in production
4. **Logging**: Ensure sensitive data (Aadhaar numbers, OTPs) are masked in logs
5. **Validation**: The API includes Aadhaar number validation using Verhoeff algorithm

## Testing

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

## Deployment

### Docker

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app
COPY . .
ENTRYPOINT ["dotnet", "AadhaarEkyc.Api.dll"]
```

### Azure App Service

1. Configure application settings for UIDAI configuration
2. Upload certificates to App Service certificates or use Key Vault
3. Enable HTTPS and configure custom domains

## Troubleshooting

### Common Issues

1. **Certificate Loading Errors**: Ensure certificates are in correct format and passwords are correct
2. **UIDAI Connection Issues**: Verify network connectivity and certificate configuration
3. **Validation Errors**: Check Aadhaar number format and OTP validity

### Logs

Check application logs for detailed error information:

```bash
# View logs in development
dotnet run --verbosity detailed
```

## License

This project is licensed under the MIT License.
