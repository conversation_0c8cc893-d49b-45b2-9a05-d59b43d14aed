{"format": 1, "restore": {"D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Api\\AadhaarEkyc.Api.csproj": {}}, "projects": {"D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Api\\AadhaarEkyc.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Api\\AadhaarEkyc.Api.csproj", "projectName": "AadhaarEkyc.Api", "projectPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Api\\AadhaarEkyc.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Application\\AadhaarEkyc.Application.csproj": {"projectPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Application\\AadhaarEkyc.Application.csproj"}, "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Infrastructure\\AadhaarEkyc.Infrastructure.csproj": {"projectPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Infrastructure\\AadhaarEkyc.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Application\\AadhaarEkyc.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Application\\AadhaarEkyc.Application.csproj", "projectName": "AadhaarEkyc.Application", "projectPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Application\\AadhaarEkyc.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Domain\\AadhaarEkyc.Domain.csproj": {"projectPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Domain\\AadhaarEkyc.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[9.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Domain\\AadhaarEkyc.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Domain\\AadhaarEkyc.Domain.csproj", "projectName": "AadhaarEkyc.Domain", "projectPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Domain\\AadhaarEkyc.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Infrastructure\\AadhaarEkyc.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Infrastructure\\AadhaarEkyc.Infrastructure.csproj", "projectName": "AadhaarEkyc.Infrastructure", "projectPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Infrastructure\\AadhaarEkyc.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Application\\AadhaarEkyc.Application.csproj": {"projectPath": "D:\\Augment-projects\\AUA_KUA\\Aadhaar_ekyc_api\\src\\AadhaarEkyc.Application\\AadhaarEkyc.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}