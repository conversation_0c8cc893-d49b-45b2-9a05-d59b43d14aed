﻿namespace AadhaarEkyc.Domain.Entities;

public class EkycRequest
{
    public string Uid { get; set; } = string.Empty;
    public string Txn { get; set; } = string.Empty;
    public string AuaCode { get; set; } = string.Empty;
    public string SubAuaCode { get; set; } = string.Empty;
    public string LicenseKey { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string? Otp { get; set; }
    public string? ShareCode { get; set; }
}

public class EkycResponse
{
    public string Ret { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Txn { get; set; } = string.Empty;
    public string? EkycXml { get; set; }
    public string? ErrorInfo { get; set; }
    public DateTime Timestamp { get; set; }
}

public class OtpRequest
{
    public string Uid { get; set; } = string.Empty;
    public string AuaCode { get; set; } = string.Empty;
    public string SubAuaCode { get; set; } = string.Empty;
    public string LicenseKey { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string? ShareCode { get; set; }
}

public class OtpResponse
{
    public string Ret { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Txn { get; set; } = string.Empty;
    public string? ErrorInfo { get; set; }
    public DateTime Timestamp { get; set; }
}
